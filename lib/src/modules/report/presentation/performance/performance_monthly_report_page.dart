import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_monthly_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_monthly_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/performance_monthly_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';
import 'package:koc_app/src/shared/widgets/charts/dual_axis_line_chart.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class PerformanceMonthlyReportPage extends StatefulWidget {
  const PerformanceMonthlyReportPage({super.key});

  @override
  State<PerformanceMonthlyReportPage> createState() => _PerformanceMonthlyReportPageState();
}

class _PerformanceMonthlyReportPageState
    extends BasePageState<PerformanceMonthlyReportPage, PerformanceMonthlyReportCubit>
    with ReportMixin, CommonMixin, FilterMixin {
  @override
  void initState() {
    Modular.get<FilterCubit>().clear();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Performance'),
        customAction: Row(
          children: [
            BlocBuilder<PerformanceMonthlyReportCubit, PerformanceMonthlyReportState>(builder: (_, state) {
              if (state != PerformanceMonthlyReportState() && state.reportData.isNotEmpty) {
                return IconButton(
                  icon: Icon(
                    Icons.file_download_outlined,
                    size: 20.r,
                  ),
                  onPressed: () {
                    saveCsvToDownloadFolder(context, convertToCsv(state), _getFileName());
                  },
                );
              }
              return const SizedBox.shrink();
            }),
            IconButton(
                onPressed: () async {
                  bool? showReport = await showFilters(
                      context,
                      [
                        ReportPeriod.LAST_12_MONTHS,
                        ReportPeriod.THIS_YEAR,
                        ReportPeriod.LAST_YEAR,
                        ReportPeriod.CUSTOM_RANGE
                      ],
                      showSites: true);
                  if (showReport != null && showReport) {
                    _findConversions();
                  }
                },
                icon: const Icon(Icons.tune)),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  String _getFileName() {
    FilterState reportFilterState = Modular.get<FilterCubit>().state;
    DateTimeRange dateTimeRange =
        getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);
    DateFormat formatter = DateFormat(yearMonthFormat);
    return 'PerformanceMonthlyReport-${formatter.format(dateTimeRange.start)}-${formatter.format(dateTimeRange.end)}';
  }

  Future<void> _findConversions() async {
    cubit.showLoading();
    FilterState reportFilterState = Modular.get<FilterCubit>().state;
    DateTimeRange dateTimeRange =
        getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);
    Modular.get<FilterCubit>().selectRangeDate(dateTimeRange.start, dateTimeRange.end);
    await cubit.findConversions(dateTimeRange.start, dateTimeRange.end, reportFilterState.selectedDateType,
        reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
    cubit.hideLoading();
  }

  /// Refresh data for pull-to-refresh
  Future<void> _refreshData() async {
    final FilterState reportFilterState = Modular.get<FilterCubit>().state;

    // Only refresh if we have an active filter/report showing
    if (reportFilterState != FilterState() && cubit.state.showReport) {
      final DateTimeRange dateTimeRange =
          getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);

      // Use pullToRefresh method which clears cache before fetching new data
      await cubit.pullToRefresh(dateTimeRange.start, dateTimeRange.end, reportFilterState.selectedDateType,
          reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
    }
  }

  List<List<dynamic>> convertToCsv(PerformanceMonthlyReportState state) {
    List<List<dynamic>> result = [
      [
        'Month',
        'Clicks',
        'Conversions',
        'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})',
        'CVR',
        'EPC'
      ]
    ];

    for (var report in state.reportData) {
      result.add([
        report.month,
        report.clicks.toCommaSeparated(),
        report.conversions.toCommaSeparated(),
        report.reward.toCommaSeparated(),
        report.conversionRate.toPercentage(),
        report.earningsPerClick.toCommaSeparated(),
      ]);
    }
    return result;
  }

  Widget _buildBody() {
    return BlocBuilder<PerformanceMonthlyReportCubit, PerformanceMonthlyReportState>(builder: (_, state) {
      if (state.showReport) {
        return SingleChildScrollView(
          child: Column(
            children: [
              BlocBuilder<FilterCubit, FilterState>(
                bloc: Modular.get<FilterCubit>(),
                builder: (_, reportFilterState) {
                  if (state.showReport && reportFilterState != FilterState()) {
                    return buildFilterRow(context, reportFilterState, showSites: true, periodTemplate: [
                      ReportPeriod.LAST_12_MONTHS,
                      ReportPeriod.THIS_YEAR,
                      ReportPeriod.LAST_YEAR,
                    ]);
                  }
                  return const SizedBox.shrink();
                },
              ),
              if (state.reportData.isNotEmpty) _buildConversionResultBody(state),
              if (state.reportData.isEmpty)
                buildNoResultBody(context, () {
                  cubit.hideReport();
                  Modular.get<FilterCubit>().clear();
                })
            ],
          ),
        ).withPullToRefresh(
          onRefresh: _refreshData,
        );
      }
      return buildAddFilterMessage(context);
    });
  }

  Widget _buildConversionResultBody(PerformanceMonthlyReportState state) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPerformanceChart(state),
          _buildConversionTable(state),
        ],
      ),
    );
  }

  Widget _buildConversionTable(PerformanceMonthlyReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0x1F000000),
            width: 1.r,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Row(
          children: [
            DataTable(
              headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
              columns: [
                buildDataColumn(context, 'Month'),
              ],
              rows: state.reportData.map((conversion) {
                return DataRow(cells: [
                  buildDataCell(context,
                      DateFormat(abbreviateMonthYearFormat).format(conversion.month!.toDateTime(yearMonthFormat)),
                      color: const Color(0xFFEF6507), onTap: () {
                    Modular.to.pushNamed('/report/performance-daily', arguments: conversion.month);
                  }),
                ]);
              }).toList(),
            ),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
                  columns: [
                    buildDataColumn(context, 'Clicks'),
                    buildDataColumn(context, 'Conversions'),
                    buildDataColumn(context, 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})'),
                    buildDataColumn(context, 'CVR'),
                    buildDataColumn(context, 'EPC'),
                  ],
                  rows: state.reportData.map((conversion) {
                    return DataRow(cells: [
                      buildDataCell(context, conversion.clicks.toCommaSeparated()),
                      buildDataCell(context, conversion.conversions.toCommaSeparated()),
                      buildDataCell(context, conversion.reward.toCommaSeparated()),
                      buildDataCell(context, conversion.conversionRate.toPercentage()),
                      buildDataCell(context, conversion.earningsPerClick.toCommaSeparated()),
                    ]);
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<num> _getValues(PerformanceChartTitle title, List<PerformanceMonthlyReportData> reportData) {
    if (title == PerformanceChartTitle.CLICKS) {
      return reportData.map((e) => e.clicks).toList();
    } else if (title == PerformanceChartTitle.CONVERSIONS) {
      return reportData.map((e) => e.conversions).toList();
    } else if (title == PerformanceChartTitle.EPC) {
      return reportData.map((e) => e.earningsPerClick).toList();
    } else if (title == PerformanceChartTitle.REWARD) {
      return reportData.map((e) => e.reward).toList();
    } else if (title == PerformanceChartTitle.CVR) {
      return reportData.map((e) => e.conversionRate).toList();
    }
    return [];
  }

  Widget _buildPerformanceChart(PerformanceMonthlyReportState state) {
    final List<String> labels = state.reportData
        .map((data) => DateFormat(abbreviateMonthYearFormat).format(data.month!.toDateTime(yearMonthFormat)))
        .toList();
    final List<num> leftCounts = _getValues(state.left, state.reportData);
    final List<num> rightCounts = _getValues(state.right, state.reportData);

    final chartData = DualAxisChartData(
      labels: labels,
      leftAxisData: leftCounts,
      rightAxisData: rightCounts,
      leftAxisLabel: state.left.value,
      rightAxisLabel: state.right.value,
    );

    const chartConfig = DualAxisChartConfig(
      showLegend: true,
      aspectRatio: 1.5,
      legendPosition: LegendPosition.below,
      normalHeight: 280.0,
      emptyHeight: 200.0,
      showContainer: false,
    );

    return Container(
      height: 280.r,
      padding: EdgeInsets.symmetric(horizontal: 4.r, vertical: 12.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 16.r),
            child: Row(
              spacing: 16.r,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: PopupMenuButton<PerformanceChartTitle>(
                    color: Colors.white,
                    onSelected: (value) {
                      cubit.updateChartTitles(value, state.right);
                    },
                    itemBuilder: (context) {
                      return _getTitles(state.right).map((e) {
                        return PopupMenuItem(
                            value: e,
                            child: Text(
                              e.value,
                              style: context.textLabelLarge(),
                            ));
                      }).toList();
                    },
                    child: _buildChartTitle(state.left.value),
                  ),
                ),
                Text(
                  'vs',
                  style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                ),
                Expanded(
                  child: PopupMenuButton<PerformanceChartTitle>(
                    color: Colors.white,
                    onSelected: (value) {
                      cubit.updateChartTitles(state.left, value);
                    },
                    itemBuilder: (context) {
                      return _getTitles(state.left).map((e) {
                        return PopupMenuItem(
                            value: e,
                            child: Text(
                              e.value,
                              style: context.textLabelLarge(),
                            ));
                      }).toList();
                    },
                    child: _buildChartTitle(state.right.value),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: DualAxisLineChart(
              data: chartData,
              config: chartConfig,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartTitle(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.white,
        border: Border.all(
          width: 1.r,
          color: ColorConstants.borderColor,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: context.textLabelLarge()),
          Icon(
            Icons.arrow_drop_down_outlined,
            size: 12.r,
          ),
        ],
      ),
    );
  }

  List<PerformanceChartTitle> _getTitles(PerformanceChartTitle otherSide) {
    return List.from(PerformanceChartTitle.values)..remove(otherSide);
  }
}
