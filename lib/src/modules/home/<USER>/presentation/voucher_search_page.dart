import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_search_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_search_state.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/modules/shared/model/identifiable.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/empty_state_widget.dart';

class VoucherSearchPage extends StatefulWidget {
  const VoucherSearchPage({super.key});

  @override
  State<VoucherSearchPage> createState() => _VoucherSearchPageState();
}

class _VoucherSearchPageState extends BasePageState<VoucherSearchPage, VoucherSearchCubit> {
  @override
  void initState() {
    super.initState();
    cubit.resetSearchState();
    findData();
  }

  Future<void> findData() async {
    await cubit.findCampaigns();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Search'),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(8.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCampaignSelector(),
          _buildVouchers(),
        ],
      ),
    );
  }

  Widget _buildVouchers() {
    return BlocBuilder<VoucherSearchCubit, VoucherSearchState>(builder: (context, state) {
      if (state.selectedCampaign == null) {
        return Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Symbols.quick_reference_all,
                  weight: 200,
                  size: 80.r,
                  color: const Color(0xFFFFB522),
                ),
                SizedBox(height: 16.r),
                Text(
                  'Select campaign filter to find a voucher code',
                  style: Theme.of(context).textTheme.labelLarge,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }
      if (state.isSearching) {
        return const Expanded(child: SizedBox.shrink());
      }
      if (state.searchResult.isEmpty) {
        return Expanded(
          child: EmptyStateWidget(
            onButtonPressed: () async {
              cubit.selectCampaign(null);
              cubit.clearVouchers();
            },
          ),
        );
      }

      return Expanded(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(8.0.r),
            child: Column(spacing: 8.r, children: state.searchResult.map((voucher) => VoucherCard(voucher)).toList()),
          ),
        ),
      );
    });
  }

  Widget _buildVoucherButton(IconData icon, VoidCallback action) {
    return GestureDetector(
      onTap: action,
      child: CircleAvatar(
        backgroundColor: Colors.white,
        radius: 18.r,
        child: Icon(
          icon,
          size: 20.r,
        ),
      ),
    );
  }

  Widget _buildCampaignSelector() {
    return GestureDetector(
      onTap: () {
        _showModalBottomSheet();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 6.r),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.r), border: Border.all(color: Colors.grey)),
        child: BlocBuilder<VoucherSearchCubit, VoucherSearchState>(builder: (context, state) {
          if (state.selectedCampaign == null) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Campaign',
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                Icon(
                  Icons.arrow_drop_down_rounded,
                  size: 18.r,
                ),
              ],
            );
          } else {
            return Text(
              state.selectedCampaign!.name,
              style: Theme.of(context).textTheme.labelLarge,
            );
          }
        }),
      ),
    );
  }

  void _showModalBottomSheet() {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25.r),
                topRight: Radius.circular(25.r),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(
                        Symbols.close,
                        weight: 400,
                        size: 25.r,
                      ),
                    ),
                    SizedBox(width: 12.r),
                    Text(
                      'Campaign',
                      style: Theme.of(context).appBarTheme.titleTextStyle,
                    ),
                  ],
                ),
                SizedBox(height: 20.r),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: BlocBuilder<VoucherSearchCubit, VoucherSearchState>(
                          bloc: cubit,
                          builder: (context, state) {
                            int length = state.campaigns.isNotEmpty ? state.campaigns.length * 2 - 1 : 0;
                            return SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: List.generate(length, (index) {
                                  if (index.isEven) {
                                    Identifiable campaign = state.campaigns[index ~/ 2];
                                    return GestureDetector(
                                      onTap: () async {
                                        Navigator.pop(context);
                                        cubit.showLoading();
                                        cubit.selectCampaign(campaign);
                                        await cubit.findVouchers();
                                        cubit.hideLoading();
                                      },
                                      child: Container(
                                          width: double.infinity,
                                          color: state.selectedCampaign == campaign ? Colors.amber : Colors.white,
                                          padding: EdgeInsets.only(left: 12.r, right: 12.r, top: 8.r, bottom: 8.r),
                                          child: Text(campaign.name,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .labelLarge
                                                  ?.copyWith(fontWeight: FontWeight.w500))),
                                    );
                                  } else {
                                    return Divider(
                                      color: const Color(0xFFE7E7E7),
                                      height: 1.r,
                                    );
                                  }
                                }),
                              ),
                            );
                          }),
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }
}
